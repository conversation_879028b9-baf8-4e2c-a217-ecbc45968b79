{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/renderer/src/env.d.ts", "src/renderer/src/**/*", "src/renderer/src/**/*.tsx", "src/preload/*.d.ts", "src/shared/**/*"], "compilerOptions": {"composite": true, "jsx": "react-jsx", "baseUrl": ".", "noUnusedLocals": false, "paths": {"@renderer/*": ["src/renderer/src/*"], "@/*": ["src/renderer/src/*"], "@shared/*": ["src/shared/*"]}}}